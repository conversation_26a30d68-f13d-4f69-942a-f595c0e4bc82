<template>
  <div class="flex flex-col h-full">
    <!-- Header - Mobile Only -->
    <div class="block md:hidden mb-3">
      <div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
        <div class="text-primary font-semibold text-sm">
          {{
            payment?.methodDescription || payment?.methodCode || paymentMethod
          }}
        </div>
        <button
          @click="handleClickBack"
          class="p-1 hover:bg-gray-200 rounded transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4 text-gray-600"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col items-center justify-center min-h-0">
      <!-- QR Code Display -->
      <div class="flex-1 flex items-center justify-center w-full">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center">
          <div
            class="bg-gray-200 animate-pulse rounded-xl w-80 h-80 flex items-center justify-center"
          >
            <svg
              class="w-12 h-12 text-gray-400"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M4 3a2 2 0 00-2 2v1.5h16V5a2 2 0 00-2-2H4zm14 4.5H2V15a2 2 0 002 2h12a2 2 0 002-2V7.5zM5 9a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>

        <!-- QR Code Image -->
        <div
          v-else-if="
            (payment?.qrCodeUrl || payment?.qrCode || payment?.payUrl) &&
            paymentMethod !== 'payon'
          "
          class="flex items-center justify-center"
        >
          <div class="bg-white p-3 rounded-xl shadow-lg border border-gray-200">
            <img
              :src="
                paymentMethod === 'transfer'
                  ? `${payment?.qrCodeUrl || payment?.payUrl}`
                  : `${payment?.qrCodeUrl || payment?.qrCode}`
              "
              alt="QR Code"
              class="w-80 h-80 object-contain"
              loading="lazy"
            />
          </div>
        </div>

        <!-- PayOn Success Message -->
        <div
          v-else-if="paymentMethod === 'payon'"
          class="flex flex-col items-center space-y-3 text-center"
        >
          <div
            class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-8 h-8 text-green-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div>
            <h4 class="text-lg font-bold text-green-700 mb-1">Thành công!</h4>
            <p class="text-sm text-gray-600">
              Đã tạo thanh toán mPos thành công
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons & Error -->
    <div class="flex-shrink-0 space-y-2">
      <!-- Error Message -->
      <div
        v-if="isExpired"
        class="bg-red-50 border border-red-200 rounded-lg p-2 mx-auto max-w-md"
      >
        <div class="flex items-center space-x-2">
          <svg
            class="w-4 h-4 text-red-500 flex-shrink-0"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <p class="text-xs text-red-700 font-medium">
            Giao dịch đã hết hạn. Vui lòng tạo lại.
          </p>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3 w-full max-w-sm mx-auto">
        <button
          @click="createNewPayment"
          class="flex-1 flex items-center justify-center px-3 py-2 text-primary bg-white border-2 border-primary rounded-lg font-medium hover:bg-primary hover:text-white transition-all duration-200 text-sm"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Tạo mới
        </button>
        <button
          @click="toogleConfirmPayment"
          class="flex-1 flex items-center justify-center px-3 py-2 text-white bg-primary rounded-lg font-medium hover:bg-primary/90 transition-all duration-200 text-sm"
        >
          <svg
            class="w-4 h-4 mr-1"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
          Xác nhận
        </button>
      </div>
    </div>

    <!-- Modal -->
    <ModalConfirmPayment
      v-if="isModalOpen"
      :isEmployee="true"
      :isOpen="isModalOpen"
      @update:isOpen="isModalOpen = $event"
      @confirm="handleConfirm"
    />
  </div>
</template>
<script setup lang="ts">
const props = defineProps([
  "payment",
  "paymentMethod",
  "isExpired",
  "isLoading",
]);
const emits = defineEmits(["backQr", "createNewPayment"]);

const handleClickBack = () => {
  emits("backQr");
};
const createNewPayment = () => {
  emits("createNewPayment");
};
const isModalOpen = ref(false);
const toogleConfirmPayment = async () => {
  isModalOpen.value = !isModalOpen.value;
};
const { confirmPayment } = usePayment();
const handleConfirm = async (data: any) => {
  await confirmPayment(
    props.payment?.paymentId,
    data?.transactionCode,
    data?.note,
    data.employee
  );
};
</script>
