<template>
  <div class="w-full">
    <!-- Mobile Layout -->
    <div class="block md:hidden">
      <div class="bg-white shadow-sm">
        <!-- Payment Methods Selection -->
        <div v-if="!isOpenQr" class="p-3 space-y-2">
          <h2 class="text-base font-bold text-gray-900 mb-3">
            <PERSON><PERSON><PERSON>h<PERSON><PERSON> thức thanh toán
          </h2>
          <div
            v-for="item in dataPaymentMethod"
            :key="item.code"
            :class="[
              'border-2 rounded-xl p-4 flex items-center gap-3 cursor-pointer transition-all duration-200',
              selectedPaymentMethod === item.code
                ? 'border-primary bg-primary/5 shadow-md'
                : 'border-gray-200 hover:border-primary/50 hover:shadow-sm',
            ]"
            @click="selectPaymentMethod(item)"
          >
            <div class="flex-shrink-0">
              <NuxtImg
                class="w-8 h-8 object-contain"
                :src="item.image || 'https://placehold.co/0'"
                :alt="item.name"
                loading="lazy"
                preload
              />
            </div>
            <span class="font-semibold text-base text-gray-900">{{
              item.name
            }}</span>
            <div v-if="selectedPaymentMethod === item.code" class="ml-auto">
              <div
                class="w-5 h-5 bg-primary rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Processing -->
        <div v-else class="p-3">
          <div
            v-if="
              selectedPaymentMethod === 'manual' ||
              selectedPaymentMethod === 'wallet'
            "
          >
            <ManualPayment
              v-if="selectedPaymentMethod === 'manual'"
              :payment="dataPayment"
              :paymentMethod="selectedPaymentMethod"
              :orderDetails="orderDetails"
              :paymentAmount="paymentAmount"
              @backQr="handleBack"
              @createManualSuccess="createManualSuccess"
            />
            <WalletPayment
              v-if="selectedPaymentMethod === 'wallet'"
              :payment="dataPayment"
              :paymentMethod="selectedPaymentMethod"
              :orderDetails="orderDetails"
              :paymentAmount="paymentAmount"
              :paymentWallet="paymentStore.customerWallet"
              @backQr="handleBack"
              @createManualSuccess="createManualSuccess"
            />
          </div>
          <QrPayment
            v-else
            :payment="dataPayment"
            :paymentMethod="selectedPaymentMethod"
            :isExpired="isExpired"
            :isLoading="isLoading"
            @backQr="handleBack"
            @createNewPayment="handleCreateNewPayment"
          />
        </div>
      </div>
    </div>
    <!-- Desktop Layout -->
    <div class="hidden md:block">
      <div class="h-[calc(100vh-6.5rem)] flex gap-2 p-2">
        <!-- Left Sidebar - Payment Methods & Share Link -->
        <div class="w-1/3 flex flex-col gap-2 h-full">
          <!-- Payment Methods -->
          <div
            class="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col flex-1 min-h-0"
          >
            <div class="p-4 border-b border-gray-200 flex-shrink-0">
              <h2 class="text-base font-bold text-gray-900">
                Phương thức thanh toán
              </h2>
            </div>
            <div class="flex-1 overflow-y-auto p-4">
              <div class="space-y-3">
                <div
                  v-for="item in dataPaymentMethod"
                  :key="item.code"
                  :class="[
                    'border-2 rounded-xl p-4 flex items-center gap-3 cursor-pointer transition-all duration-200',
                    selectedPaymentMethod === item.code
                      ? 'border-primary bg-primary/5 shadow-md'
                      : 'border-gray-200 hover:border-primary/50 hover:shadow-sm',
                  ]"
                  @click="selectPaymentMethod(item)"
                >
                  <div class="flex-shrink-0">
                    <NuxtImg
                      class="w-8 h-8 object-contain"
                      :src="item.image || 'https://placehold.co/0'"
                      :alt="item.name"
                      loading="lazy"
                      preload
                    />
                  </div>
                  <span class="font-semibold text-base text-gray-900 flex-1">{{
                    item.name
                  }}</span>
                  <div
                    v-if="selectedPaymentMethod === item.code"
                    class="flex-shrink-0"
                  >
                    <div
                      class="w-5 h-5 bg-primary rounded-full flex items-center justify-center"
                    >
                      <svg
                        class="w-3 h-3 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Share Link -->
          <div class="flex-shrink-0">
            <ShareLink :orderDetails="orderDetails" />
          </div>
        </div>

        <!-- Right Content Area - Payment Processing -->
        <div class="flex-1 flex flex-col h-full">
          <div
            class="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col h-full"
          >
            <!-- Order Information Header -->
            <div class="border-b border-gray-200 p-4 flex-shrink-0">
              <InforOrderPayment
                @updatePrintOrder="handleUpdateQuantityPrintOrder"
                :orderDetails="orderDetails"
                :paymentAmount="paymentAmount"
                :paymentWallet="paymentStore.customerWallet"
              />
            </div>

            <!-- Payment Content -->
            <div class="flex-1 overflow-y-auto p-4">
              <QrPayment
                v-if="
                  isOpenQr &&
                  selectedPaymentMethod !== 'manual' &&
                  selectedPaymentMethod !== 'wallet'
                "
                :payment="dataPayment"
                :paymentMethod="selectedPaymentMethod"
                :isExpired="isExpired"
                :isLoading="isLoading"
                @backQr="handleBack"
                @createNewPayment="handleCreateNewPayment"
              />
              <div v-else-if="selectedPaymentMethod">
                <ManualPayment
                  v-if="selectedPaymentMethod === 'manual'"
                  :payment="dataPayment"
                  :paymentMethod="selectedPaymentMethod"
                  :orderDetails="orderDetails"
                  :paymentAmount="paymentAmount"
                  @backQr="handleBack"
                  @createManualSuccess="createManualSuccess"
                />
                <WalletPayment
                  v-if="selectedPaymentMethod === 'wallet'"
                  :payment="dataPayment"
                  :paymentMethod="selectedPaymentMethod"
                  :orderDetails="orderDetails"
                  :paymentAmount="paymentAmount"
                  :paymentWallet="paymentStore.customerWallet"
                  @backQr="handleBack"
                  @createManualSuccess="createManualSuccess"
                />
              </div>
              <div v-else class="flex items-center justify-center h-full">
                <div class="text-center">
                  <div class="text-gray-400 mb-4">
                    <svg
                      class="w-16 h-16 mx-auto"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <p class="text-gray-500 text-lg">
                    Vui lòng chọn phương thức thanh toán
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Dialog v-if="isAlert" @confirm="confirm" @cancel="cancel"></Dialog>
    <ConfirmDialog
      v-if="isOpenWalletMessage"
      :title="'Số dư ví không đủ'"
      :message="'Số dư ví không đủ vui lòng lựa chọn phương thức thanh toán khác'"
      @confirm="handleConfirmWalletMessage"
      :isHideButton="true"
    ></ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
const ConfirmDialog = defineAsyncComponent(
  () => import("~/components/dialog/ConfirmDialog.vue")
);
const Dialog = defineAsyncComponent(
  () => import("~/components/Payment/Dialog.vue")
);
const WalletPayment = defineAsyncComponent(
  () => import("~/components/Payment/WalletPayment.vue")
);
const ManualPayment = defineAsyncComponent(
  () => import("~/components/Payment/ManualPayment.vue")
);
import type { Auth } from "~/types/Auth";

const props = defineProps([
  "orderDetails",
  "dataPaymentMethod",
  "paymentAmount",
]);
const { createPaymentOrder, cancelPayment, paymentsByOrders } = usePayment();
const selectedPaymentMethod = ref("manual");
const route = useRoute();
const isOpenQr = ref(false);
const dataPayment = ref();
const auth = useCookie("auth").value as unknown as Auth;
const { storeId, orgId } = useTabContext();

const isLoading = ref(false);
const paymentStore = usePaymentStore();
const dataPaymentOrder = computed(() => paymentStore.dataPaymentOrder);
const isOpenWalletMessage = ref(false);
// hàm tạo thanh toán
const handleCreateOrder = async (orderId: string, paymentMethod: string) => {
  const host = window.location.origin;
  let amount;

  if (props.paymentAmount > 0) {
    amount = props.paymentAmount;
  } else {
    amount = props.orderDetails.data.remainTotal;
  }
  const data = {
    orderId: orderId,
    paymentMethod: paymentMethod,
    appliedAmount: amount,
    payDate: Date.now(),
    source: "ORDER_SOURCE",
    returnUrl: `${host}/thanh-toan`,
    paymentType: "ONLINE",
    createBy: auth?.user?.id,
    attributes: {
      returnQR: false,
    },
  };
  try {
    const response = await createPaymentOrder(data);
    return response;
  } catch (error) {
    throw error;
  }
};
const selectPaymentMethod = async (item: any) => {
  // clear interval
  clearInterval(intervalId);
  selectedPaymentMethod.value = item.code;
  if (
    selectedPaymentMethod.value !== "manual" &&
    selectedPaymentMethod.value !== "wallet"
  ) {
    isLoading.value = true;
    // kiểm tra lịch sử thanh toán
    if (dataPaymentOrder.value?.length > 0) {
      const response = dataPaymentOrder.value.findLast(
        (payment: any) => payment.methodTypeCode === selectedPaymentMethod.value
      );

      // console.log("response abc", response);
      if (response) {
        // kiểm tra tiếp về giá tiền
        if (props.paymentAmount > 0) {
          if (response.totalAmount === props.paymentAmount) {
            isOpenQr.value = true;
            dataPayment.value = response;
            intervalId = setInterval(handleCheckPaymentStatus, 5000);
          } else {
            const response = dataPaymentOrder.value.find(
              (item: any) =>
                item?.methodTypeCode === selectedPaymentMethod.value
            );
            if (response) {
              await cancelPayment(response?.paymentId, "");
              const payment = await handleCreateOrder(
                props.orderDetails.data.id,
                selectedPaymentMethod.value
              );
              if (payment) {
                isOpenQr.value = true;
                dataPayment.value = payment;
                // mở hàm gọi 5s
                intervalId = setInterval(handleCheckPaymentStatus, 5000);
              }
            }
          }
        } else {
          isOpenQr.value = true;
          dataPayment.value = response;
          intervalId = setInterval(handleCheckPaymentStatus, 5000);
        }
        //
      } else {
        const payment = await handleCreateOrder(
          props.orderDetails.data.id,
          selectedPaymentMethod.value
        );
        if (payment) {
          isOpenQr.value = true;
          dataPayment.value = payment;
          // mở hàm gọi 5s
          intervalId = setInterval(handleCheckPaymentStatus, 5000);
        }
      }
    } else {
      const payment = await handleCreateOrder(
        props.orderDetails.data.id,
        selectedPaymentMethod.value
      );
      if (payment) {
        isOpenQr.value = true;
        dataPayment.value = payment;
        // mở hàm gọi 5s
        intervalId = setInterval(handleCheckPaymentStatus, 5000);
      }
    }
  } else {
    // nếu phương thức thanh toán là tiền mặt
    isOpenQr.value = true;
  }
  await paymentStore.getDataPaymentOrder(props.orderDetails.data.id);

  isLoading.value = false;
};
const handleBack = async () => {
  clearInterval(intervalId);
  isOpenQr.value = false;
  selectedPaymentMethod.value = "";
};
const handleCreateNewPayment = async () => {
  isExpired.value = false;
  // tìm và hủy đúng phương thức thanh toán đang chờ
  if (dataPaymentOrder.value?.length > 0) {
    const response = dataPaymentOrder.value.find(
      (item: any) => item?.methodTypeCode === selectedPaymentMethod.value
    );
    if (response) {
      await cancelPayment(response?.paymentId, "");
    }
  }
  //tạo thanh toán
  const payment = await handleCreateOrder(
    props.orderDetails.data.id,
    selectedPaymentMethod.value
  );
  dataPayment.value = payment;
  await paymentStore.getDataPaymentOrder(props.orderDetails.data.id);
  clearInterval(intervalId);
  intervalId = setInterval(handleCheckPaymentStatus, 5000);
};
// hàm check status mỗi 5s
const isAlert = ref(false);
const isExpired = ref(false);
const orderStore = useOrderStore();
const { fetchOrderDetails, updateStatusApproved } = useOrder();

const handleCheckPaymentStatus = async () => {
  // nếu pttt là tiền mặt hoặc ví thì alert + clear
  if (
    selectedPaymentMethod.value === "manual" ||
    selectedPaymentMethod.value === "wallet"
  ) {
    isAlert.value = true;
    clearInterval(intervalId);
    return;
  }

  if (dataPaymentOrder.value?.length > 0) {
    const response = await paymentsByOrders([props.orderDetails.data.id]);

    // const result = dataPaymentOrder.value.find(
    //   (item: any) => item?.methodTypeCode === selectedPaymentMethod.value
    // );
    const result = dataPaymentOrder.value.findLast(
      (item: any) => item?.methodTypeCode === selectedPaymentMethod.value
    );

    const lastPaymentId = response.find(
      (payment: any) => payment?.paymentId === result?.paymentId
    );
    if (lastPaymentId?.statusCode === "0") {
      //await updateStatusApproved(props.orderDetails.data.id);
      orderStore.paymentAmount = 0;
      localStorage.removeItem("paymentAmount");
      isAlert.value = true;
      clearInterval(intervalId);
    }
    if (lastPaymentId?.statusCode === "-1") {
      isExpired.value = true;
      clearInterval(intervalId);
    }
  } else {
    await paymentStore.getDataPaymentOrder(props.orderDetails.data.id);
  }
};
let intervalId: any;
onUnmounted(() => {
  clearInterval(intervalId);
});
//
const confirm = async (value: boolean) => {
  isAlert.value = value;
  navigateTo(`/sale?orgId=${orgId.value}&storeId=${storeId.value}`);
};
const cancel = () => {
  isAlert.value = false;
};
const createManualSuccess = () => {
  isAlert.value = true;
  clearInterval(intervalId);
};
const handleLastPayment = () => {
  // console.log("dataPaymentOrder", dataPaymentOrder.value);
  // nếu 1 phần thì kiểm tra có đúng tiền hay k

  if (dataPaymentOrder.value?.length > 0) {
    const lastItem = dataPaymentOrder.value[dataPaymentOrder.value?.length - 1];
    if (lastItem?.methodCode === "refund") {
      selectedPaymentMethod.value = "manual";
      return;
    }
    if (props.paymentAmount > 0) {
      if (lastItem?.totalAmount === props.paymentAmount) {
        selectedPaymentMethod.value = lastItem?.methodTypeCode;
        dataPayment.value = lastItem;
        isOpenQr.value = true;
        intervalId = setInterval(handleCheckPaymentStatus, 5000);
      }
    } else {
      if (!(props.orderDetails?.data?.remainTotal > 0)) {
        selectedPaymentMethod.value = lastItem?.methodTypeCode;
        dataPayment.value = lastItem;
        isOpenQr.value = true;
        intervalId = setInterval(handleCheckPaymentStatus, 5000);
      }
    }
  }
};
onMounted(async () => {
  await paymentStore.getDataPaymentOrder(route.query.orderId as string),
    handleLastPayment();
});
const router = useRouter();
router.beforeEach(async (to, from, next) => {
  if (to.path === `/payment`) {
    selectedPaymentMethod.value = "manual";
    isExpired.value = false;
    isAlert.value = false;
    dataPayment.value = {};
    clearInterval(intervalId);
    isOpenQr.value = false;
  }
  if (from.path === `/payment`) {
    clearInterval(intervalId);
  }
  next();
});
watch(
  () => route.query.orderId,
  async (newVal, oldVal) => {
    if (newVal) {
      await paymentStore.getDataPaymentOrder(newVal as string),
        handleLastPayment();
    }
  }
);
watch(
  () => selectedPaymentMethod.value,
  (newVal: string, oldVal: string) => {
    if (newVal === "manual") {
      clearInterval(intervalId);
    }
  }
);
//
const handleConfirmWalletMessage = () => {
  isOpenWalletMessage.value = false;
};
// Import utilities
import { updatePrintCount } from "~/utils/orderHelpers";

const handleUpdateQuantityPrintOrder = () => {
  if (props.orderDetails?.data?.order) {
    updatePrintCount(props.orderDetails.data.order);
  }
};
</script>
