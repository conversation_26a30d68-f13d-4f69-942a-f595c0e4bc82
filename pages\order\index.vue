<template>
  <div class="flex flex-col h-full">
    <!-- <PERSON><PERSON> tìm kiếm + nút tạo đơn -->
    <div class="mt-2 mx-2 space-y-2">
      <SearchOrder
        :dataEmployee="diaryStore.dataEmployee"
        :dataPaymentMethod="dataPaymentMethod"
        :dataOrderStatus="dataOrderStatus"
        @handleSearch="handleSearch"
      />

      <div class="flex justify-between items-center pb-2">
        <TabChangeOrder :tabs="tabs" @toogleTab="handleSetTab" />

        <button
          @click="handleNavigate"
          class="flex items-center gap-2 bg-primary text-white px-3 py-1 rounded-md transition duration-200"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 4v16m8-8H4"
            />
          </svg>
          <PERSON><PERSON><PERSON> đơn
        </button>
      </div>

      <!-- <PERSON><PERSON><PERSON> báo không tìm thấy đơn -->
      <p v-if="ordersStore.isAlert" class="text-sm text-red-500">
        Không tìm thấy đơn hàng
      </p>
    </div>

    <!-- Danh sách đơn hàng -->
    <div class="md:mx-2 flex-1">
      <!-- Loading toàn trang -->
      <LoadingDiary v-if="loading" :isPageOrder="true" />

      <!-- Table cho desktop -->
      <div v-if="!loading" class="hidden md:block">
        <!-- Scrollable Table Body -->
        <div
          ref="scrollContainer"
          class="bg-white rounded-b-md overflow-y-auto max-h-[calc(100vh-200px)]"
        >
          <table class="w-full text-sm table-auto bg-white">
            <thead
              class="sticky top-0 z-10 bg-blue-100 text-left font-semibold"
            >
              <tr>
                <th class="p-2 w-1/12 text-center">Mã đơn</th>
                <th class="p-2 w-2/12">Khách hàng</th>
                <th class="p-2 w-2/12">Nhân viên</th>
                <th class="p-2 w-4/12 text-center">Sản phẩm</th>
                <th class="p-2 w-1/12">Tổng giảm</th>
                <th class="p-2 w-2/12">Thanh toán</th>
                <th class="p-2 w-auto text-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-5 h-5 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                    />
                  </svg>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="diary in ordersStore.dataListOrder"
                :key="diary.id"
                class="even:bg-gray-50 odd:bg-white hover:bg-blue-50"
              >
                <TableDiary
                  :diary="diary"
                  :isNotDraft="true"
                  :isFFM="true"
                  :data="data"
                  @handleLoading="toggleLoading"
                />
              </tr>
            </tbody>
          </table>

          <LoadingDiary v-if="isLoading" />
          <div class="mb-[100px]"></div>
        </div>
      </div>

      <!-- Danh sách đơn mobile -->
      <div v-if="!loading" class="block md:hidden space-y-2 mt-2">
        <div
          ref="mobileScrollContainer"
          class="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto"
        >
          <QuickOrder
            v-for="item in ordersStore.dataListOrder"
            :key="item.id"
            :dataOrder="item"
            :data="data"
            @handleLoading="toggleLoading"
          />
        </div>
        <!-- Padding cho cuối trang -->
        <div class="mb-[140px]"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const TableDiary = defineAsyncComponent(
  () => import("~/components/Diary/TableDiary.vue")
);
const QuickOrder = defineAsyncComponent(
  () => import("~/components/Order/QuickOrder.vue")
);

// SEO
useHead({
  title: "Danh sách đơn hàng",
  meta: [{ name: "description", content: "Danh sách đơn hàng" }],
});

// PAGE META
definePageMeta({
  layout: "dashboard",
  middleware: ["auth", "permission", "store"],
  name: "Đơn hàng",
});

// STORE
const ordersStore = useOrdersStore();
const diaryStore = useDiariesStore();
const router = useRouter();

// STATE
const isReturnOrder = ref(false);
const scrollContainer = ref<HTMLElement | null>(null);
const isLoading = ref(false);
const hasMoreData = ref(true);
const loading = ref(false);
const loadingNavigate = ref(false);

// OPTIONS
const orderOptions = reactive({
  currentPage: 1,
  maxResult: 10,
  status_ignore: [1],
});

// TABS
const tabs = ref([
  { label: "Đơn Bán hàng", value: "SELL" },
  { label: "Đơn trả hàng", value: "RETURN" },
]);

// SEARCH & FILTER DATA
const dataPaymentMethod = ref();
const dataOrderStatus = ref();

// FETCH COMPOSABLES
const {
  fetchListSellOrder,
  fetchListSellOrderReturn,
  fetchListSaleOrderStatus,
} = useOrder();
const { getPaymentMethodTypes } = usePayment();

// LAZY LOADING SETTING DATA
const { data } = await useFetch("/data/setting.json");

// 🔁 DEBOUNCE SCROLL
function debounce<T extends (...args: any[]) => any>(fn: T, delay: number): T {
  let timeout: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: any[]) {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), delay);
  } as T;
}

// 📦 LOAD DATA EMPLOYEE + STATUS + PAYMENT
async function fetchSearchMeta() {
  try {
    dataPaymentMethod.value = await getPaymentMethodTypes();
    const response = await fetchListSaleOrderStatus();
    dataOrderStatus.value = response?.data;
  } catch (error) {
    console.error("Error loading search metadata", error);
  }
}

// 📄 LOAD ORDER LIST (for paging, infinite scroll, etc.)
async function loadOrders() {
  if (orderOptions.currentPage === 1 || isLoading.value || !hasMoreData.value)
    return;
  isLoading.value = true;

  try {
    const fetchFn = isReturnOrder.value
      ? fetchListSellOrderReturn
      : fetchListSellOrder;
    const response = await fetchFn(orderOptions);
    const newData = response.data?.data || [];

    if (newData.length) {
      newData.forEach((item: any) => ordersStore.addOrder(item));
      orderOptions.currentPage++;
    } else {
      hasMoreData.value = false;
    }
  } catch (error) {
    console.error("Error loading orders", error);
  } finally {
    isLoading.value = false;
  }
}

// 🔍 HANDLE SEARCH ACTION
async function handleSearch(filters: Record<string, any>) {
  if (loading.value) return;
  loading.value = true;

  Object.assign(orderOptions, {
    currentPage: 1,
    date_create_to: filters?.date_create_to,
    date_create_from: filters?.date_create_from,
    employee_assign: filters?.employee_assign,
    keyword: filters?.keyword,
    payment_method: filters?.payment_method,
    customer_multi_value: filters?.customer_multi_value,
    product_multi_value: filters?.product_multi_value,
    status: [filters?.status],
    exist_ffm_status: filters?.exist_ffm_status,
    ffm_status: filters?.ffm_status,
  });

  const fetchFn = isReturnOrder.value
    ? ordersStore.getListDataReturnOrder
    : ordersStore.getListDataOrder;
  await fetchFn(orderOptions);

  orderOptions.currentPage = 2;
  hasMoreData.value = true;
  loading.value = false;
}

// 🧭 HANDLE NAVIGATE TO CREATE ORDER
async function handleNavigate() {
  let isTimeout = false;
  const timer = setTimeout(() => {
    isTimeout = true;
    loadingNavigate.value = true;
  }, 150);

  // Use tab-isolated context instead of cookies
  const { storeId, orgId } = useTabContext();

  await router.push(`/sale?orgId=${orgId.value}&storeId=${storeId.value}`);
  clearTimeout(timer);

  if (isTimeout) loadingNavigate.value = false;
}

// 🔀 HANDLE TAB SWITCH
async function handleSetTab(tab: string) {
  isReturnOrder.value = tab === "RETURN";
  orderOptions.currentPage = 1;
  isLoading.value = false;
  hasMoreData.value = true;
  loading.value = true;

  const fetchFn = isReturnOrder.value
    ? ordersStore.getListDataReturnOrder
    : ordersStore.getListDataOrder;
  await fetchFn(orderOptions);

  orderOptions.currentPage = 2;
  loading.value = false;
}

// 🔁 INIT PAGE
onMounted(async () => {
  ordersStore.tooltip = null;
  localStorage.setItem("paymentAmount", "0");

  loading.value = true;
  await Promise.allSettled([
    handleSetTab("SELL"),
    diaryStore.handleGetDataEmployee(),
    fetchSearchMeta(),
  ]);
  loading.value = false;
});

// INFINITE SCROLL
useInfiniteScroll(scrollContainer, debounce(loadOrders, 100), {
  distance: 100,
});

// 🔄 TRIGGER LOADING FROM CHILD
const toggleLoading = (state: boolean) => {
  loadingNavigate.value = state;
};
</script>
